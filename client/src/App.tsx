import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import {
  Dashboard,
  ManajemenPengunjung,
  ManajemenPengguna,
  ManajemenKonten,
  RoleManagement,
  BackupRestore,
  Logs
} from './pages/superadmin';
import { SuperAdminLayout } from './pages/superadmin/SuperAdminLayout';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Navigate to="/superadmin/dashboard" replace />} />
        <Route path="/superadmin/dashboard" element={
          <SuperAdminLayout>
            <Dashboard />
          </SuperAdminLayout>
        } />
        <Route path="/superadmin/manajemen-pengunjung" element={
          <SuperAdminLayout>
            <ManajemenPengunjung />
          </SuperAdminLayout>
        } />
        <Route path="/superadmin/pengaturan-sistem" element={
          <SuperAdminLayout>
            <div className="text-center py-12">
              <h2 className="text-xl font-semibold text-gray-900">Pengaturan Sistem</h2>
              <p className="text-gray-600 mt-2">Halaman ini sedang dalam pengembangan</p>
            </div>
          </SuperAdminLayout>
        } />
        <Route path="/superadmin/manajemen-pengguna" element={
          <SuperAdminLayout>
            <ManajemenPengguna />
          </SuperAdminLayout>
        } />
        <Route path="/superadmin/manajemen-konten" element={
          <SuperAdminLayout>
            <ManajemenKonten />
          </SuperAdminLayout>
        } />
        <Route path="/superadmin/role-management" element={
          <SuperAdminLayout>
            <RoleManagement />
          </SuperAdminLayout>
        } />
        <Route path="/superadmin/backup-restore" element={
          <SuperAdminLayout>
            <BackupRestore />
          </SuperAdminLayout>
        } />
        <Route path="/superadmin/logs" element={
          <SuperAdminLayout>
            <Logs />
          </SuperAdminLayout>
        } />
      </Routes>
    </Router>
  );
}

export default App