import { useState } from 'react';
import { Card, CardContent } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Badge } from '../../components/ui/badge';
import { Download, RotateCcw, Trash2 } from 'lucide-react';

interface BackupLog {
  id: string;
  date: string;
  type: 'Auto-Backup' | 'Manual';
  status: 'Berhasil' | 'Gagal';
  size: string;
}

export function BackupRestore() {
  const [backupSchedule, setBackupSchedule] = useState('daily');
  const [backupLogs] = useState<BackupLog[]>([
    {
      id: '1',
      date: '2023-10-01',
      type: 'Auto-Backup',
      status: 'Berhasil',
      size: '1.5 GB'
    },
    {
      id: '2',
      date: '2023-09-24',
      type: 'Manual',
      status: 'Gagal',
      size: '1.2 GB'
    },
    {
      id: '3',
      date: '2023-09-15',
      type: 'Auto-Backup',
      status: 'Berhasil',
      size: '1.3 GB'
    }
  ]);

  const getStatusBadge = (status: string) => {
    return status === 'Berhasil' 
      ? <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Berhasil</Badge>
      : <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Gagal</Badge>;
  };

  const BackupContent = () => (
    <div className="space-y-6">
      {/* Auto Backup Schedule & Export/Import */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Auto Backup Schedule */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Auto Backup Schedule</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Backup Frequency
                </label>
                <select
                  value={backupSchedule}
                  onChange={(e) => setBackupSchedule(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                >
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>
              <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white">
                Update Schedule
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Export/Import */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Export/Import Data</h3>
            <div className="space-y-3">
              <Button 
                variant="outline" 
                className="w-full justify-start"
              >
                <Download className="h-4 w-4 mr-2" />
                Export All Data
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start"
              >
                <Download className="h-4 w-4 mr-2" />
                Export User Data
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start"
              >
                <Download className="h-4 w-4 mr-2" />
                Export Visitor Data
              </Button>
              <div className="pt-2">
                <input
                  type="file"
                  accept=".json,.csv"
                  className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-orange-50 file:text-orange-700 hover:file:bg-orange-100"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Manual Backup */}
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Manual Backup</h3>
            <Button className="bg-orange-500 hover:bg-orange-600 text-white">
              Create Backup Now
            </Button>
          </div>
          <p className="text-sm text-gray-600">
            Create an immediate backup of all system data. This will include user data, visitor records, and system configurations.
          </p>
        </CardContent>
      </Card>

      {/* Backup History */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Backup History</h3>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="text-left p-3 font-medium text-gray-900">Date</th>
                  <th className="text-left p-3 font-medium text-gray-900">Type</th>
                  <th className="text-left p-3 font-medium text-gray-900">Status</th>
                  <th className="text-left p-3 font-medium text-gray-900">Size</th>
                  <th className="text-left p-3 font-medium text-gray-900">Actions</th>
                </tr>
              </thead>
              <tbody>
                {backupLogs.map((log) => (
                  <tr key={log.id} className="border-b hover:bg-gray-50">
                    <td className="p-3 text-gray-900">{log.date}</td>
                    <td className="p-3 text-gray-600">{log.type}</td>
                    <td className="p-3">{getStatusBadge(log.status)}</td>
                    <td className="p-3 text-gray-600">{log.size}</td>
                    <td className="p-3">
                      <div className="flex space-x-2">
                        <Button size="sm" variant="ghost" className="text-blue-600 hover:text-blue-800">
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost" className="text-green-600 hover:text-green-800">
                          <RotateCcw className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost" className="text-red-600 hover:text-red-800">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="space-y-4 lg:space-y-6 max-w-full">
      {/* Page Header */}
      <div className="flex-shrink-0">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900 truncate">Backup & Restore</h1>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="PAM" className="w-full">
        <div className="flex-shrink-0">
          <TabsList className="grid w-full grid-cols-3 max-w-sm">
            <TabsTrigger
              value="PAM"
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              PAM
            </TabsTrigger>
            <TabsTrigger
              value="SMA"
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              SMA
            </TabsTrigger>
            <TabsTrigger
              value="IBM"
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              IBM
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="PAM" className="mt-4 lg:mt-6">
          <BackupContent />
        </TabsContent>

        <TabsContent value="SMA" className="mt-4 lg:mt-6">
          <BackupContent />
        </TabsContent>

        <TabsContent value="IBM" className="mt-4 lg:mt-6">
          <BackupContent />
        </TabsContent>
      </Tabs>
    </div>
  );
}
