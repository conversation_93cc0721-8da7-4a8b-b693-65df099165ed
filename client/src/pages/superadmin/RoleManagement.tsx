/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Plus, Edit, Trash2 } from 'lucide-react';
import { Modal } from '../../components/ui/modal';
import { Input } from '../../components/ui/input';
import { Textarea } from '../../components/ui/textarea';
import { SimpleSelect } from '../../components/ui/simple-select';

interface Permission {
  id: string;
  name: string;
  create: boolean;
  read: boolean;
  update: boolean;
  delete: boolean;
}

interface Role {
  id: string;
  name: string;
  description: string;
  company?: string;
  permissions: Permission[];
}

export function RoleManagement() {
  const [roles, setRoles] = useState<Role[]>([]);

  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showPermissionModal, setShowPermissionModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);

  // Form states
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    company: ''
  });

  const [editingPermissions, setEditingPermissions] = useState<Permission[]>([]);

  const togglePermission = (permissionId: string, type: 'create' | 'read' | 'update' | 'delete') => {
    setEditingPermissions(prev =>
      prev.map(permission =>
        permission.id === permissionId
          ? { ...permission, [type]: !permission[type] }
          : permission
      )
    );
  };

  const defaultPermissions: Permission[] = [
    { id: '1', name: 'Dashboard', create: false, read: true, update: false, delete: false },
    { id: '2', name: 'Manajemen Pengunjung', create: false, read: true, update: false, delete: false },
    { id: '3', name: 'Manajemen Pengguna', create: false, read: false, update: false, delete: false },
    { id: '4', name: 'Manajemen Konten', create: false, read: false, update: false, delete: false },
    { id: '5', name: 'Role Management', create: false, read: false, update: false, delete: false },
    { id: '6', name: 'Backup & Restore', create: false, read: false, update: false, delete: false },
    { id: '7', name: 'Logs', create: false, read: false, update: false, delete: false },
  ];

  useEffect(() => {
    // Mock data
    setRoles([
      {
        id: '1',
        name: 'Super Admin',
        description: 'Full access to all features',
        permissions: defaultPermissions.map(p => ({ ...p, create: true, read: true, update: true, delete: true }))
      },
      {
        id: '2',
        name: 'Admin PAM',
        description: 'Admin access for PAM company',
        company: 'PAM',
        permissions: defaultPermissions.map(p => 
          p.name === 'Manajemen Pengguna' || p.name === 'Role Management' || p.name === 'Backup & Restore'
            ? p
            : { ...p, create: true, read: true, update: true, delete: false }
        )
      },
      {
        id: '3',
        name: 'User',
        description: 'Basic user access',
        permissions: defaultPermissions.map(p => 
          p.name === 'Dashboard' || p.name === 'Manajemen Pengunjung'
            ? { ...p, read: true }
            : p
        )
      }
    ]);
  }, []);

  const handleAddRole = () => {
    if (formData.name && formData.description) {
      const newRole: Role = {
        id: Date.now().toString(),
        name: formData.name,
        description: formData.description,
        company: formData.company || undefined,
        permissions: [...defaultPermissions]
      };
      setRoles([...roles, newRole]);
      setFormData({ name: '', description: '', company: '' });
      setShowAddModal(false);
    }
  };

  const handleEditRole = (role: Role) => {
    setSelectedRole(role);
    setFormData({
      name: role.name,
      description: role.description,
      company: role.company || ''
    });
    setShowEditModal(true);
  };

  const handleUpdateRole = () => {
    if (selectedRole && formData.name && formData.description) {
      setRoles(roles.map(role => 
        role.id === selectedRole.id 
          ? { 
              ...role, 
              name: formData.name, 
              description: formData.description,
              company: formData.company || undefined
            }
          : role
      ));
      setFormData({ name: '', description: '', company: '' });
      setSelectedRole(null);
      setShowEditModal(false);
    }
  };

  const handleManagePermissions = (role: Role) => {
    setSelectedRole(role);
    setEditingPermissions([...role.permissions]);
    setShowPermissionModal(true);
  };

  const handleSavePermissions = () => {
    if (selectedRole) {
      setRoles(roles.map(role => 
        role.id === selectedRole.id 
          ? { ...role, permissions: [...editingPermissions] }
          : role
      ));
      setSelectedRole(null);
      setEditingPermissions([]);
      setShowPermissionModal(false);
    }
  };

  const handleDeleteRole = () => {
    if (selectedRole) {
      setRoles(roles.filter(role => role.id !== selectedRole.id));
      setSelectedRole(null);
      setShowDeleteModal(false);
    }
  };

  const openDeleteModal = (role: Role) => {
    setSelectedRole(role);
    setShowDeleteModal(true);
  };

  const closeModals = () => {
    setShowAddModal(false);
    setShowEditModal(false);
    setShowPermissionModal(false);
    setShowDeleteModal(false);
    setSelectedRole(null);
    setFormData({ name: '', description: '', company: '' });
    setEditingPermissions([]);
  };

  return (
    <div className="space-y-4 lg:space-y-6 max-w-full">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900 truncate">Role Management</h1>
        <Button 
          onClick={() => setShowAddModal(true)}
          className="bg-orange-500 hover:bg-orange-600 text-white"
        >
          <Plus className="h-4 w-4 mr-2" />
          Tambah Role
        </Button>
      </div>

      {/* Roles Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {roles.map((role) => (
          <Card key={role.id} className="border border-gray-200 hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="space-y-3">
                <div>
                  <h3 className="font-semibold text-gray-900">{role.name}</h3>
                  {role.company && (
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                      {role.company}
                    </span>
                  )}
                </div>
                
                <p className="text-sm text-gray-600">{role.description}</p>
                
                <div className="text-xs text-gray-500">
                  Permissions: {role.permissions.filter(p => p.read || p.create || p.update || p.delete).length} / {role.permissions.length}
                </div>
                
                <div className="flex space-x-2 pt-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleEditRole(role)}
                    className="flex-1"
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    Edit
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleManagePermissions(role)}
                    className="flex-1"
                  >
                    Permissions
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => openDeleteModal(role)}
                    className="text-red-600 hover:text-red-800"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Add Role Modal */}
      <Modal
        isOpen={showAddModal}
        onClose={closeModals}
        title="Tambah Role Baru"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Nama Role
            </label>
            <Input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Masukkan nama role"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Perusahaan (Opsional)
            </label>
            <SimpleSelect
              value={formData.company}
              onValueChange={(value) => setFormData({ ...formData, company: value })}
              placeholder="Pilih perusahaan"
              options={[
                { value: 'PAM', label: 'PAM' },
                { value: 'SMA', label: 'SMA' },
                { value: 'IBM', label: 'IBM' }
              ]}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Deskripsi
            </label>
            <Textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Masukkan deskripsi role"
              rows={3}
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={closeModals}>
              Batal
            </Button>
            <Button
              onClick={handleAddRole}
              className="bg-orange-500 hover:bg-orange-600 text-white"
            >
              Tambah
            </Button>
          </div>
        </div>
      </Modal>

      {/* Edit Role Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={closeModals}
        title="Edit Role"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Nama Role
            </label>
            <Input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Masukkan nama role"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Perusahaan (Opsional)
            </label>
            <SimpleSelect
              value={formData.company}
              onValueChange={(value) => setFormData({ ...formData, company: value })}
              placeholder="Pilih perusahaan"
              options={[
                { value: 'PAM', label: 'PAM' },
                { value: 'SMA', label: 'SMA' },
                { value: 'IBM', label: 'IBM' }
              ]}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Deskripsi
            </label>
            <Textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Masukkan deskripsi role"
              rows={3}
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={closeModals}>
              Batal
            </Button>
            <Button
              onClick={handleUpdateRole}
              className="bg-orange-500 hover:bg-orange-600 text-white"
            >
              Update
            </Button>
          </div>
        </div>
      </Modal>

      {/* Manage Permissions Modal */}
      <Modal
        isOpen={showPermissionModal}
        onClose={closeModals}
        title={`Manage Permissions - ${selectedRole?.name}`}
        size="lg"
      >
        <div className="space-y-4">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2 font-medium">Permission</th>
                  <th className="text-center p-2 font-medium">Create</th>
                  <th className="text-center p-2 font-medium">Read</th>
                  <th className="text-center p-2 font-medium">Update</th>
                  <th className="text-center p-2 font-medium">Delete</th>
                </tr>
              </thead>
              <tbody>
                {editingPermissions.map((permission) => (
                  <tr key={permission.id} className="border-b">
                    <td className="p-2 font-medium">{permission.name}</td>
                    <td className="text-center p-2">
                      <input
                        type="checkbox"
                        checked={permission.create}
                        onChange={() => togglePermission(permission.id, 'create')}
                        className="text-orange-500"
                      />
                    </td>
                    <td className="text-center p-2">
                      <input
                        type="checkbox"
                        checked={permission.read}
                        onChange={() => togglePermission(permission.id, 'read')}
                        className="text-orange-500"
                      />
                    </td>
                    <td className="text-center p-2">
                      <input
                        type="checkbox"
                        checked={permission.update}
                        onChange={() => togglePermission(permission.id, 'update')}
                        className="text-orange-500"
                      />
                    </td>
                    <td className="text-center p-2">
                      <input
                        type="checkbox"
                        checked={permission.delete}
                        onChange={() => togglePermission(permission.id, 'delete')}
                        className="text-orange-500"
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={closeModals}>
              Batal
            </Button>
            <Button
              onClick={handleSavePermissions}
              className="bg-orange-500 hover:bg-orange-600 text-white"
            >
              Simpan Permissions
            </Button>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={closeModals}
        title="Konfirmasi Hapus"
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            Apakah Anda yakin ingin menghapus role <strong>{selectedRole?.name}</strong>?
            Tindakan ini tidak dapat dibatalkan.
          </p>

          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={closeModals}>
              Batal
            </Button>
            <Button
              onClick={handleDeleteRole}
              className="bg-red-500 hover:bg-red-600 text-white"
            >
              Hapus
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
}
