/* eslint-disable @typescript-eslint/no-unused-vars */
import { useState } from 'react';
import { Card, CardContent } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Upload, GripVertical, Save, X } from 'lucide-react';
import { Input } from '../../components/ui/input';
import { Textarea } from '../../components/ui/textarea';

interface Question {
  id: string;
  question: string;
  options: { id: string; text: string; isCorrect: boolean }[];
}

export function ManajemenKonten() {
  // const [activeTab, setActiveTab] = useState('PAM');
  const [isEditing, setIsEditing] = useState(false);
  const [questions, setQuestions] = useState<Question[]>([
    {
      id: '1',
      question: 'Apa tujuan utama dari safety induction?',
      options: [
        { id: '1a', text: 'Hijau: petunjuk/informasi', isCorrect: false },
        { id: '1b', text: 'Kuning: peringatan', isCorrect: false },
        { id: '1c', text: 'Hitam: Beracun', isCorrect: true },
        { id: '1d', text: 'Merah: larangan', isCorrect: false }
      ]
    },
    {
      id: '2',
      question: 'Apakah yang harus dilakukan Visitor jika melihat percikan api/kebakaran?',
      options: [
        { id: '2a', text: 'Melaporkan ke pendamping, dalam situasi darurat boleh melakukan pemadaman dengan pemahaman APAR', isCorrect: true },
        { id: '2b', text: 'Langsung padamkan api menggunakan air terdekat', isCorrect: false },
        { id: '2c', text: 'Mencari karung goni untuk memadamkan api', isCorrect: false },
        { id: '2d', text: 'Berteriak sekencang-kencangnya', isCorrect: false }
      ]
    }
  ]);

  const VideoUploadSection = ({ title }: { title: string }) => (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900">{title}</h3>
      <Card className="border-2 border-dashed border-gray-300 bg-gray-50">
        <CardContent className="p-6 text-center">
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900">Upload Video</h4>
            <p className="text-sm text-gray-600">Max 500MB</p>
            <Button variant="outline" className="mt-4">
              <Upload className="h-4 w-4 mr-2" />
              Pilih File
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const QuizSection = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Quiz Questions</h3>
        <div className="space-x-2">
          {isEditing ? (
            <>
              <Button 
                variant="outline" 
                onClick={() => setIsEditing(false)}
                size="sm"
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button 
                onClick={() => setIsEditing(false)}
                className="bg-orange-500 hover:bg-orange-600 text-white"
                size="sm"
              >
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>
            </>
          ) : (
            <Button 
              onClick={() => setIsEditing(true)}
              variant="outline"
              size="sm"
            >
              Edit Questions
            </Button>
          )}
        </div>
      </div>

      <div className="space-y-4">
        {questions.map((question, index) => (
          <Card key={question.id} className="border border-gray-200">
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                {isEditing && (
                  <div className="flex-shrink-0 mt-2">
                    <GripVertical className="h-5 w-5 text-gray-400 cursor-move" />
                  </div>
                )}
                
                <div className="flex-1 space-y-3">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-500">
                      Question {index + 1}
                    </span>
                  </div>
                  
                  {isEditing ? (
                    <Textarea
                      value={question.question}
                      onChange={(e) => {
                        const updatedQuestions = questions.map(q =>
                          q.id === question.id ? { ...q, question: e.target.value } : q
                        );
                        setQuestions(updatedQuestions);
                      }}
                      className="w-full"
                      rows={2}
                    />
                  ) : (
                    <p className="text-gray-900 font-medium">{question.question}</p>
                  )}

                  <div className="space-y-2">
                    {question.options.map((option, optionIndex) => (
                      <div key={option.id} className="flex items-center space-x-3">
                        <span className="text-sm text-gray-500 w-6">
                          {String.fromCharCode(65 + optionIndex)}.
                        </span>
                        
                        {isEditing ? (
                          <div className="flex-1 flex items-center space-x-2">
                            <Input
                              value={option.text}
                              onChange={(e) => {
                                const updatedQuestions = questions.map(q =>
                                  q.id === question.id
                                    ? {
                                        ...q,
                                        options: q.options.map(opt =>
                                          opt.id === option.id
                                            ? { ...opt, text: e.target.value }
                                            : opt
                                        )
                                      }
                                    : q
                                );
                                setQuestions(updatedQuestions);
                              }}
                              className="flex-1"
                            />
                            <input
                              type="radio"
                              name={`correct-${question.id}`}
                              checked={option.isCorrect}
                              onChange={() => {
                                const updatedQuestions = questions.map(q =>
                                  q.id === question.id
                                    ? {
                                        ...q,
                                        options: q.options.map(opt => ({
                                          ...opt,
                                          isCorrect: opt.id === option.id
                                        }))
                                      }
                                    : q
                                );
                                setQuestions(updatedQuestions);
                              }}
                              className="text-orange-500"
                            />
                            <label className="text-sm text-gray-600">Correct</label>
                          </div>
                        ) : (
                          <div className="flex-1 flex items-center space-x-2">
                            <span className={`flex-1 ${option.isCorrect ? 'text-green-700 font-medium' : 'text-gray-700'}`}>
                              {option.text}
                            </span>
                            {option.isCorrect && (
                              <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                                Correct
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {isEditing && (
        <Button 
          variant="outline" 
          className="w-full border-dashed border-gray-300 text-gray-600 hover:text-gray-900"
        >
          + Add New Question
        </Button>
      )}
    </div>
  );

  return (
    <div className="space-y-4 lg:space-y-6 max-w-full">
      {/* Page Header */}
      <div className="flex-shrink-0">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900 truncate">Manajemen Konten</h1>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="PAM" className="w-full">
        <div className="flex-shrink-0">
          <TabsList className="grid w-full grid-cols-3 max-w-sm">
            <TabsTrigger
              value="PAM"
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              PAM
            </TabsTrigger>
            <TabsTrigger
              value="SMA"
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              SMA
            </TabsTrigger>
            <TabsTrigger
              value="IBM"
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              IBM
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="PAM" className="space-y-6 mt-4 lg:mt-6">
          <VideoUploadSection title="Video Safety Induction PAM" />
          <QuizSection />
        </TabsContent>

        <TabsContent value="SMA" className="space-y-6 mt-4 lg:mt-6">
          <VideoUploadSection title="Video Safety Induction SMA" />
          <QuizSection />
        </TabsContent>

        <TabsContent value="IBM" className="space-y-6 mt-4 lg:mt-6">
          <VideoUploadSection title="Video Safety Induction IBM" />
          <QuizSection />
        </TabsContent>
      </Tabs>
    </div>
  );
}
